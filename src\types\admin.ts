export interface AdminUser {
  id: string;
  username: string;
  password: string;
  email: string;
  role: 'admin' | 'moderator';
  lastLogin?: Date;
}

export interface SiteSettings {
  siteName: string;
  siteNameAr: string;
  contactEmail: string;
  whatsappNumber: string;
  socialLinks: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };
  heroImages: string[];
  aboutText: string;
  aboutTextAr: string;
  address: string;
  addressAr: string;
  phone: string;
  workingHours: string;
  workingHoursAr: string;
}

export interface Category {
  id: string;
  name: string;
  nameAr: string;
  description?: string;
  descriptionAr?: string;
  image?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subcategory {
  id: string;
  name: string;
  nameAr: string;
  categoryId: string;
  description?: string;
  descriptionAr?: string;
  image?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SpecificationItem {
  nameEn: string;
  nameAr: string;
  valueEn: string;
  valueAr: string;
}

export interface AdminProduct {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  images: string[];
  price: number;
  originalPrice?: number;
  available: boolean;
  categoryId: string;
  subcategoryId: string;
  features: string[];
  featuresAr: string[];
  specifications: SpecificationItem[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSubcategories: number;
  activeProducts: number;
  inactiveProducts: number;
  featuredProducts: number;
}

export interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

export interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

export interface CompanySettings {
  email: string;
  whatsapp: string;
}
