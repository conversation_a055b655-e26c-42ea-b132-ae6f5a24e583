'use client';

import React from 'react';
import { Locale } from '../lib/i18n';

interface PartnersSectionProps {
  locale: Locale;
}

const PartnersSection: React.FC<PartnersSectionProps> = ({ locale }) => {
  const partners = [
    {
      name: 'Hilton Hotels',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hilton-Logo.png',
      description: locale === 'ar' ? 'شريك موثوق في قطاع الفندقة' : 'Trusted partner in hospitality'
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Marriott-Logo.png',
      description: locale === 'ar' ? 'تعاون استراتيجي مميز' : 'Distinguished strategic cooperation'
    },
    {
      name: 'Four Seasons',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Four-Seasons-Logo.png',
      description: locale === 'ar' ? 'شراكة في التميز والجودة' : 'Partnership in excellence and quality'
    },
    {
      name: 'Hyatt',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hyatt-Logo.png',
      description: locale === 'ar' ? 'علاقة طويلة الأمد' : 'Long-term relationship'
    },
    {
      name: 'InterContinental',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/InterContinental-Logo.png',
      description: locale === 'ar' ? 'شريك عالمي معتمد' : 'Certified global partner'
    },
    {
      name: 'Radisson',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Radisson-Logo.png',
      description: locale === 'ar' ? 'تعاون مثمر ومستمر' : 'Fruitful and continuous cooperation'
    }
  ];

  const content = {
    ar: {
      title: 'شركاؤنا',
      subtitle: 'نفخر بثقة كبرى الشركات والفنادق العالمية بنا',
      description: 'نعمل مع أفضل الشركات في قطاع الضيافة لتوفير أعلى معايير الجودة والخدمة',
      trustText: 'يثق بنا أكثر من',
      clientsCount: '500+',
      clientsText: 'عميل حول العالم'
    },
    en: {
      title: 'Our Partners',
      subtitle: 'We are proud of the trust of major international companies and hotels',
      description: 'We work with the best companies in the hospitality sector to provide the highest standards of quality and service',
      trustText: 'Trusted by more than',
      clientsCount: '500+',
      clientsText: 'clients worldwide'
    }
  };

  const currentContent = content[locale];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            {currentContent.title}
          </h2>
          <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
          <p className="text-gray-500 max-w-2xl mx-auto">
            {currentContent.description}
          </p>
        </div>

        {/* Stats */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-4 bg-white rounded-2xl px-8 py-6 shadow-lg">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">
                {currentContent.clientsCount}
              </div>
              <div className="text-sm text-gray-600">
                {currentContent.clientsText}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">15+</div>
              <div className="text-sm text-gray-600">
                {locale === 'ar' ? 'سنة خبرة' : 'Years Experience'}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">50+</div>
              <div className="text-sm text-gray-600">
                {locale === 'ar' ? 'دولة' : 'Countries'}
              </div>
            </div>
          </div>
        </div>

        {/* Partners Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          {partners.map((partner, index) => (
            <div
              key={index}
              className="group bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
            >
              <div className="mb-4 h-16 flex items-center justify-center">
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="max-h-12 max-w-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                />
              </div>
              <h3 className="font-semibold text-gray-800 mb-2 text-sm">
                {partner.name}
              </h3>
              <p className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {partner.description}
              </p>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-primary rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'ar' 
                ? 'هل تريد أن تكون شريكنا القادم؟' 
                : 'Want to be our next partner?'
              }
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {locale === 'ar' 
                ? 'انضم إلى شبكة شركائنا المتميزين واستفد من خبرتنا الواسعة في قطاع الضيافة'
                : 'Join our distinguished partner network and benefit from our extensive experience in the hospitality sector'
              }
            </p>
            <a
              href={`/${locale}/contact`}
              className="inline-flex items-center gap-2 bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <i className="ri-handshake-line"></i>
              {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
