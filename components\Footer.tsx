import React from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';

interface FooterProps {
  locale: Locale;
}

const Footer: React.FC<FooterProps> = ({ locale }) => {
  const t = (key: string) => getTranslation(locale, key as any);

  return (
    <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-primary">DROOB HAJER</h3>
            <p className="text-gray-300 leading-relaxed">
              {locale === 'ar' 
                ? 'نحن مورد رائد لمعدات المطاعم والفنادق عالية الجودة، نقدم حلولاً شاملة لصناعة الضيافة.'
                : 'We are a leading supplier of high-quality restaurant and hotel equipment, providing comprehensive solutions for the hospitality industry.'
              }
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link href={`/${locale}`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                  {t('home')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/products`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                  {t('products')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/categories`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                  {t('categories')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/about`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                  {t('about')}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/contact`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                  {t('contact')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {locale === 'ar' ? 'معلومات التواصل' : 'Contact Info'}
            </h4>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3 space-x-reverse">
                <i className="ri-phone-line text-primary"></i>
                <span className="text-gray-300">+966 11 234 5678</span>
              </li>
              <li className="flex items-center space-x-3 space-x-reverse">
                <i className="ri-mail-line text-primary"></i>
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-3 space-x-reverse">
                <i className="ri-map-pin-line text-primary"></i>
                <span className="text-gray-300">
                  {locale === 'ar' 
                    ? 'الرياض، المملكة العربية السعودية'
                    : 'Riyadh, Saudi Arabia'
                  }
                </span>
              </li>
            </ul>
          </div>

          {/* Social Media */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {locale === 'ar' ? 'تابعنا' : 'Follow Us'}
            </h4>
            <div className="flex space-x-4 space-x-reverse">
              <a href="#" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                <i className="ri-facebook-fill"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                <i className="ri-instagram-line"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                <i className="ri-twitter-line"></i>
              </a>
              <a href="#" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                <i className="ri-linkedin-fill"></i>
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            {locale === 'ar' 
              ? '© 2024 دروب حاجر. جميع الحقوق محفوظة.'
              : '© 2024 Droob Hajer. All rights reserved.'
            }
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
