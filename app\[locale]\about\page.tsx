'use client';

import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import WhatsAppButton from '../../../components/WhatsAppButton';

export default function AboutPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const t = (key: string) => getTranslation(locale, key as any);

  const content = {
    ar: {
      title: 'من نحن',
      subtitle: 'تعرف على شركة دروب هاجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم',
      description: 'شركة دروب هاجر هي شركة رائدة في مجال توفير تجهيزات الفنادق والمطاعم بأعلى معايير الجودة والكفاءة. نحن نفخر بخدمة عملائنا منذ سنوات عديدة ونسعى دائماً لتقديم أفضل الحلول المبتكرة.',
      vision: 'رؤيتنا',
      visionText: 'أن نكون الشركة الرائدة في المملكة العربية السعودية في مجال تجهيزات الفنادق والمطاعم، ونساهم في تطوير قطاع الضيافة من خلال توفير أحدث التقنيات والمعدات عالية الجودة.',
      mission: 'مهمتنا',
      missionText: 'نلتزم بتوفير معدات وتجهيزات عالية الجودة للفنادق والمطاعم، مع تقديم خدمة عملاء متميزة وحلول مبتكرة تلبي احتياجات عملائنا وتساعدهم على تحقيق النجاح.',
      values: 'قيمنا',
      valuesItems: [
        { title: 'الجودة', description: 'نلتزم بأعلى معايير الجودة في جميع منتجاتنا وخدماتنا' },
        { title: 'الابتكار', description: 'نسعى دائماً لتقديم حلول مبتكرة ومتطورة' },
        { title: 'الثقة', description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية' },
        { title: 'التميز', description: 'نسعى للتميز في كل ما نقوم به' }
      ],
      team: 'فريقنا',
      teamText: 'يضم فريقنا مجموعة من الخبراء والمتخصصين في مجال تجهيزات الفنادق والمطاعم، الذين يعملون بجد لضمان تقديم أفضل الخدمات والحلول لعملائنا.',
      contact: 'تواصل معنا',
      contactText: 'نحن هنا لخدمتكم ومساعدتكم في جميع احتياجاتكم'
    },
    en: {
      title: 'About Us',
      subtitle: 'Learn about Droob Hajer, specialized in providing the best hotel and restaurant equipment',
      description: 'Droob Hajer is a leading company in providing hotel and restaurant equipment with the highest standards of quality and efficiency. We are proud to serve our customers for many years and always strive to provide the best innovative solutions.',
      vision: 'Our Vision',
      visionText: 'To be the leading company in Saudi Arabia in the field of hotel and restaurant equipment, and contribute to the development of the hospitality sector by providing the latest technologies and high-quality equipment.',
      mission: 'Our Mission',
      missionText: 'We are committed to providing high-quality equipment and supplies for hotels and restaurants, with excellent customer service and innovative solutions that meet our customers\' needs and help them achieve success.',
      values: 'Our Values',
      valuesItems: [
        { title: 'Quality', description: 'We are committed to the highest quality standards in all our products and services' },
        { title: 'Innovation', description: 'We always strive to provide innovative and advanced solutions' },
        { title: 'Trust', description: 'We build long-term relationships with our customers based on trust and transparency' },
        { title: 'Excellence', description: 'We strive for excellence in everything we do' }
      ],
      team: 'Our Team',
      teamText: 'Our team includes a group of experts and specialists in the field of hotel and restaurant equipment, who work hard to ensure the best services and solutions for our customers.',
      contact: 'Contact Us',
      contactText: 'We are here to serve you and help you with all your needs'
    }
  };

  const currentContent = content[locale];

  return (
    <>
      <Navbar locale={locale} />
      <main>
        {/* Hero Section */}
        <section className="bg-primary py-16">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {currentContent.title}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* About Description */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-lg text-gray-700 leading-relaxed">
                {currentContent.description}
              </p>
            </div>
          </div>
        </section>

        {/* Vision & Mission */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {/* Vision */}
              <div className="bg-white rounded-xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="ri-eye-line text-2xl text-primary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">
                    {currentContent.vision}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {currentContent.visionText}
                </p>
              </div>

              {/* Mission */}
              <div className="bg-white rounded-xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="ri-target-line text-2xl text-primary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">
                    {currentContent.mission}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {currentContent.missionText}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                {currentContent.values}
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {currentContent.valuesItems.map((value, index) => (
                <div key={index} className="text-center">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className={`ri-${['award-line', 'lightbulb-line', 'shield-check-line', 'star-line'][index]} text-3xl text-primary`}></i>
                  </div>
                  <h4 className="text-xl font-bold text-gray-800 mb-2">
                    {value.title}
                  </h4>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                {currentContent.team}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {currentContent.teamText}
              </p>
            </div>
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-16 bg-primary">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h2 className="text-3xl font-bold mb-4">
                {currentContent.contact}
              </h2>
              <p className="text-xl text-white/90 mb-8">
                {currentContent.contactText}
              </p>
              <a
                href={`/${locale}/contact`}
                className="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center gap-2"
              >
                <i className="ri-phone-line"></i>
                {t('contact')}
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
