import { SiteSettings, AdminUser } from '../types/admin';

// بيانات المستخدم الافتراضي للوحة التحكم
export const defaultAdminUser: AdminUser = {
  id: '1',
  username: 'admin',
  password: 'admin123', // في التطبيق الحقيقي يجب تشفير كلمة المرور
  email: '<EMAIL>',
  role: 'admin',
  lastLogin: new Date()
};

// إعدادات الموقع الافتراضية
export const defaultSiteSettings: SiteSettings = {
  siteName: 'VidMeet',
  siteNameAr: 'فيد ميت',
  contactEmail: '<EMAIL>',
  whatsappNumber: '+966501234567',
  socialLinks: {
    facebook: 'https://facebook.com/vidmeet',
    instagram: 'https://instagram.com/vidmeet',
    twitter: 'https://twitter.com/vidmeet',
    linkedin: 'https://linkedin.com/company/vidmeet',
    youtube: 'https://youtube.com/@vidmeet'
  },
  heroImages: [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1200&h=600&fit=crop',
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=1200&h=600&fit=crop',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=600&fit=crop'
  ],
  aboutText: 'We are a leading supplier of high-quality restaurant and hotel equipment, providing comprehensive solutions for the hospitality industry.',
  aboutTextAr: 'نحن مورد رائد لمعدات المطاعم والفنادق عالية الجودة، نقدم حلولاً شاملة لصناعة الضيافة.',
  address: '123 Business District, Riyadh, Saudi Arabia',
  addressAr: '123 الحي التجاري، الرياض، المملكة العربية السعودية',
  phone: '+966112345678',
  workingHours: 'Sunday - Thursday: 8:00 AM - 6:00 PM',
  workingHoursAr: 'الأحد - الخميس: 8:00 صباحاً - 6:00 مساءً',
  headerSettings: {
    logo: '',
    showLanguageSwitch: true,
    showSearchBar: true,
    navigationItems: [
      { nameEn: 'Home', nameAr: 'الرئيسية', url: '/', isActive: true },
      { nameEn: 'Products', nameAr: 'المنتجات', url: '/products', isActive: true },
      { nameEn: 'Categories', nameAr: 'الفئات', url: '/categories', isActive: true },
      { nameEn: 'About', nameAr: 'من نحن', url: '/about', isActive: true },
      { nameEn: 'Contact', nameAr: 'تواصل معنا', url: '/contact', isActive: true }
    ]
  },
  footerSettings: {
    copyrightText: 'All rights reserved',
    copyrightTextAr: 'جميع الحقوق محفوظة',
    showSocialLinks: true,
    quickLinks: [
      { nameEn: 'Privacy Policy', nameAr: 'سياسة الخصوصية', url: '/privacy', isActive: true },
      { nameEn: 'Terms of Service', nameAr: 'شروط الخدمة', url: '/terms', isActive: true },
      { nameEn: 'FAQ', nameAr: 'الأسئلة الشائعة', url: '/faq', isActive: true }
    ],
    companyInfo: {
      description: 'Leading supplier of restaurant and hotel equipment',
      descriptionAr: 'مورد رائد لمعدات المطاعم والفنادق',
      showAddress: true,
      showPhone: true,
      showEmail: true,
      showWorkingHours: true
    }
  }
};

// دالة للحصول على إعدادات الموقع من التخزين المحلي أو الإعدادات الافتراضية
export const getSiteSettings = (): SiteSettings => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('siteSettings');
    if (saved) {
      try {
        const parsedSettings = JSON.parse(saved);
        // دمج الإعدادات المحفوظة مع الإعدادات الافتراضية للتأكد من وجود جميع الحقول
        return {
          ...defaultSiteSettings,
          ...parsedSettings,
          headerSettings: {
            ...defaultSiteSettings.headerSettings,
            ...parsedSettings.headerSettings
          },
          footerSettings: {
            ...defaultSiteSettings.footerSettings,
            ...parsedSettings.footerSettings,
            companyInfo: {
              ...defaultSiteSettings.footerSettings.companyInfo,
              ...parsedSettings.footerSettings?.companyInfo
            }
          },
          socialLinks: {
            ...defaultSiteSettings.socialLinks,
            ...parsedSettings.socialLinks
          }
        };
      } catch (error) {
        console.error('Error parsing site settings:', error);
      }
    }
  }
  return defaultSiteSettings;
};

// دالة لحفظ إعدادات الموقع
export const saveSiteSettings = (settings: SiteSettings): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('siteSettings', JSON.stringify(settings));
  }
};

// دالة للحصول على بيانات المستخدم الإداري
export const getAdminUser = (): AdminUser => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('adminUser');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.error('Error parsing admin user:', error);
      }
    }
  }
  return defaultAdminUser;
};

// دالة لحفظ بيانات المستخدم الإداري
export const saveAdminUser = (user: AdminUser): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('adminUser', JSON.stringify(user));
  }
};

// دالة للتحقق من تسجيل الدخول
export const isLoggedIn = (): boolean => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('adminLoggedIn') === 'true';
  }
  return false;
};

// دالة لتسجيل الدخول
export const login = (username: string, password: string): boolean => {
  const adminUser = getAdminUser();
  if (adminUser.username === username && adminUser.password === password) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('adminLoggedIn', 'true');
      // تحديث وقت آخر تسجيل دخول
      adminUser.lastLogin = new Date();
      saveAdminUser(adminUser);
    }
    return true;
  }
  return false;
};

// دالة لتسجيل الخروج
export const logout = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('adminLoggedIn');
  }
};
