import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { getSiteSettings, saveSiteSettings } from '../../data/settings';
import { SiteSettings } from '../../types/admin';

const SettingsAdmin = () => {
  const [settings, setSettings] = useState<SiteSettings>({
    siteName: '',
    siteNameAr: '',
    contactEmail: '',
    whatsappNumber: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      twitter: '',
      linkedin: '',
      youtube: ''
    },
    heroImages: [''],
    aboutText: '',
    aboutTextAr: '',
    address: '',
    addressAr: '',
    phone: '',
    workingHours: '',
    workingHoursAr: ''
  });
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  useEffect(() => {
    const currentSettings = getSiteSettings();
    setSettings(currentSettings);
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveMessage('');
    
    try {
      // محاكاة تأخير الحفظ
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      saveSiteSettings(settings);
      setSaveMessage('تم حفظ الإعدادات بنجاح');
      
      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      setSaveMessage('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setIsSaving(false);
    }
  };

  const addHeroImage = () => {
    setSettings(prev => ({
      ...prev,
      heroImages: [...prev.heroImages, '']
    }));
  };

  const removeHeroImage = (index: number) => {
    setSettings(prev => ({
      ...prev,
      heroImages: prev.heroImages.filter((_, i) => i !== index)
    }));
  };

  const updateHeroImage = (index: number, value: string) => {
    setSettings(prev => ({
      ...prev,
      heroImages: prev.heroImages.map((img, i) => i === index ? value : img)
    }));
  };

  const tabs = [
    { id: 'general', title: 'عام', icon: 'ri-settings-line' },
    { id: 'contact', title: 'التواصل', icon: 'ri-phone-line' },
    { id: 'social', title: 'مواقع التواصل', icon: 'ri-share-line' },
    { id: 'hero', title: 'صور الهيرو', icon: 'ri-image-line' },
    { id: 'about', title: 'نبذة عنا', icon: 'ri-information-line' }
  ];

  return (
    <>
      <Head>
        <title>إعدادات الموقع - لوحة التحكم</title>
        <meta name="description" content="إعدادات الموقع العامة" />
      </Head>

      <AdminLayout title="إعدادات الموقع">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">إعدادات الموقع</h1>
              <p className="text-gray-600">إدارة إعدادات الموقع العامة</p>
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 disabled:opacity-50 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <i className="ri-save-line text-lg ml-2"></i>
                  حفظ الإعدادات
                </>
              )}
            </button>
          </div>

          {/* Save Message */}
          {saveMessage && (
            <div className={`p-4 rounded-lg flex items-center ${
              saveMessage.includes('نجاح') 
                ? 'bg-green-50 border border-green-200 text-green-700' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <i className={`${saveMessage.includes('نجاح') ? 'ri-check-line' : 'ri-error-warning-line'} text-lg ml-2`}></i>
              {saveMessage}
            </div>
          )}

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            {/* Tabs */}
            <div className="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
              <nav className="flex space-x-8 space-x-reverse px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-3 border-b-2 font-medium text-sm transition-all duration-300 transform hover:scale-105 ${
                      activeTab === tab.id
                        ? 'border-primary text-primary bg-primary/5 rounded-t-lg'
                        : 'border-transparent text-gray-500 hover:text-primary hover:border-primary/30 hover:bg-primary/5 rounded-t-lg'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ml-2 ${
                        activeTab === tab.id ? 'bg-primary/20 text-primary' : 'bg-gray-100 text-gray-500'
                      }`}>
                        <i className={`${tab.icon} text-lg`}></i>
                      </div>
                      {tab.title}
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">الإعدادات العامة</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الموقع بالعربية
                      </label>
                      <input
                        type="text"
                        value={settings.siteNameAr}
                        onChange={(e) => setSettings({...settings, siteNameAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="اسم الموقع بالعربية"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الموقع بالإنجليزية
                      </label>
                      <input
                        type="text"
                        value={settings.siteName}
                        onChange={(e) => setSettings({...settings, siteName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Site name in English"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان بالعربية
                      </label>
                      <textarea
                        value={settings.addressAr}
                        onChange={(e) => setSettings({...settings, addressAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="العنوان بالعربية"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان بالإنجليزية
                      </label>
                      <textarea
                        value={settings.address}
                        onChange={(e) => setSettings({...settings, address: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="Address in English"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ساعات العمل بالعربية
                      </label>
                      <input
                        type="text"
                        value={settings.workingHoursAr}
                        onChange={(e) => setSettings({...settings, workingHoursAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="الأحد - الخميس: 8:00 صباحاً - 6:00 مساءً"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ساعات العمل بالإنجليزية
                      </label>
                      <input
                        type="text"
                        value={settings.workingHours}
                        onChange={(e) => setSettings({...settings, workingHours: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Sunday - Thursday: 8:00 AM - 6:00 PM"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Contact Settings */}
              {activeTab === 'contact' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">معلومات التواصل</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        value={settings.contactEmail}
                        onChange={(e) => setSettings({...settings, contactEmail: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        value={settings.phone}
                        onChange={(e) => setSettings({...settings, phone: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="+966112345678"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الواتساب
                    </label>
                    <input
                      type="tel"
                      value={settings.whatsappNumber}
                      onChange={(e) => setSettings({...settings, whatsappNumber: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="+966501234567"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      يجب أن يتضمن رمز الدولة (مثال: +966501234567)
                    </p>
                  </div>
                </div>
              )}

              {/* Social Media Settings */}
              {activeTab === 'social' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">روابط مواقع التواصل الاجتماعي</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <i className="ri-facebook-fill text-blue-600 ml-2"></i>
                        فيسبوك
                      </label>
                      <input
                        type="url"
                        value={settings.socialLinks.facebook || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {...settings.socialLinks, facebook: e.target.value}
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://facebook.com/yourpage"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <i className="ri-instagram-fill text-pink-600 ml-2"></i>
                        إنستغرام
                      </label>
                      <input
                        type="url"
                        value={settings.socialLinks.instagram || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {...settings.socialLinks, instagram: e.target.value}
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://instagram.com/yourpage"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <i className="ri-twitter-fill text-blue-400 ml-2"></i>
                        تويتر
                      </label>
                      <input
                        type="url"
                        value={settings.socialLinks.twitter || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {...settings.socialLinks, twitter: e.target.value}
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://twitter.com/yourpage"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <i className="ri-linkedin-fill text-blue-700 ml-2"></i>
                        لينكد إن
                      </label>
                      <input
                        type="url"
                        value={settings.socialLinks.linkedin || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {...settings.socialLinks, linkedin: e.target.value}
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://linkedin.com/company/yourcompany"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <i className="ri-youtube-fill text-red-600 ml-2"></i>
                        يوتيوب
                      </label>
                      <input
                        type="url"
                        value={settings.socialLinks.youtube || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          socialLinks: {...settings.socialLinks, youtube: e.target.value}
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://youtube.com/@yourchannel"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Hero Images Settings */}
              {activeTab === 'hero' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">صور الهيرو</h3>
                  <p className="text-gray-600">إدارة الصور المعروضة في القسم الرئيسي للموقع</p>

                  <div className="space-y-4">
                    {settings.heroImages.map((image, index) => (
                      <div key={index} className="flex items-center space-x-3 space-x-reverse">
                        <div className="flex-1">
                          <input
                            type="url"
                            value={image}
                            onChange={(e) => updateHeroImage(index, e.target.value)}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="https://example.com/hero-image.jpg"
                          />
                        </div>
                        {image && (
                          <div className="w-16 h-16 rounded-lg overflow-hidden border border-gray-200">
                            <img
                              src={image}
                              alt={`Hero ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                (e.target as HTMLImageElement).src = 'https://via.placeholder.com/64x64?text=صورة';
                              }}
                            />
                          </div>
                        )}
                        {settings.heroImages.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeHeroImage(index)}
                            className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                            title="حذف الصورة"
                          >
                            <i className="ri-delete-bin-line text-lg"></i>
                          </button>
                        )}
                      </div>
                    ))}

                    <button
                      type="button"
                      onClick={addHeroImage}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center hover:bg-blue-50 px-3 py-2 rounded-lg transition-colors duration-200"
                    >
                      <i className="ri-add-line text-lg ml-1"></i>
                      إضافة صورة هيرو جديدة
                    </button>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">نصائح لصور الهيرو:</p>
                        <ul className="list-disc list-inside space-y-1 text-blue-700">
                          <li>استخدم صور عالية الجودة بدقة لا تقل عن 1200x600 بكسل</li>
                          <li>تأكد من أن الصور تعكس طبيعة عملك</li>
                          <li>استخدم صور ذات ألوان متناسقة مع تصميم الموقع</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* About Settings */}
              {activeTab === 'about' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">نبذة عن الشركة</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نبذة عن الشركة بالعربية
                    </label>
                    <textarea
                      value={settings.aboutTextAr}
                      onChange={(e) => setSettings({...settings, aboutTextAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={6}
                      placeholder="اكتب نبذة عن الشركة بالعربية..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نبذة عن الشركة بالإنجليزية
                    </label>
                    <textarea
                      value={settings.aboutText}
                      onChange={(e) => setSettings({...settings, aboutText: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={6}
                      placeholder="Write about your company in English..."
                    />
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-lightbulb-line text-gray-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-gray-700">
                        <p className="font-medium mb-1">نصائح لكتابة نبذة فعالة:</p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>اذكر تاريخ تأسيس الشركة وخبرتها</li>
                          <li>وضح الخدمات والمنتجات التي تقدمها</li>
                          <li>أبرز نقاط القوة والمميزات التنافسية</li>
                          <li>اذكر رؤية ورسالة الشركة</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default SettingsAdmin;
